import { PrismaClient } from '@prisma/client'

import { Debug } from '@thrift/common/engines/Debug'

const prisma = new PrismaClient()

;(async function () {
  // Create a sample tenant
  await prisma.tenant.upsert({
    where: { tenantUid: 'pe-de-feijao' },
    update: {},
    create: {
      id: 'fed266c7-37fe-4f80-8942-ba30b8447eff',
      name: '<PERSON><PERSON>',
      tenantUid: 'pe-de-feijao',
      balance: 1000.0,
    },
  })
})()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (error) => {
    Debug.error(error)
    await prisma.$disconnect()
    process.exit(1)
  })
