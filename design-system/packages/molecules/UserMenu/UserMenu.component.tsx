import { useEffect, useRef, useState } from 'react'
import { ChevronDown, ChevronUp } from 'lucide-react'
import type { UserMenuProps } from '@thrift/design-system/packages/molecules/UserMenu'
import * as Icons from 'lucide-react'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

const getInitials = (name: string) => {
  const names = name.trim().split(' ')

  if (names.length === 1) return names[0].charAt(0).toUpperCase()

  return (
    names[0].charAt(0).toUpperCase() +
    names[names.length - 1].charAt(0).toUpperCase()
  )
}

const getIconComponent = (iconName: string): React.ReactNode => {
  const LucideIcon = Icons[iconName as keyof typeof Icons] as React.FC<{
    className?: string
  }>

  return LucideIcon ? <LucideIcon className="w-[1.6rem] h-[1.6rem]" /> : null
}

export const UserMenu: React.FC<UserMenuProps> = ({
  name,
  userFunction,
  items,
}) => {
  const [isOpen, setIsOpen] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (ref.current && !ref.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)

    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  return (
    <div ref={ref} className="relative inline-block text-left">
      <button
        onClick={() => setIsOpen((prev) => !prev)}
        className="flex items-center gap-2"
      >
        <div className="flex items-center justify-center w-[3rem] h-[3rem] rounded-full bg-secondary border-white border-1">
          <Typography weight="bold" className="text-white">
            {getInitials(name)}
          </Typography>
        </div>
        {isOpen ? (
          <ChevronUp size="1.6rem" className="text-white" />
        ) : (
          <ChevronDown size="1.6rem" className="text-white" />
        )}
      </button>

      {isOpen && (
        <div className="absolute right-0 pb-tiny mt-2 w-[20rem] lg:w-[30rem] bg-white rounded-tiny shadow-lg border border-gray-200 z-50">
          <div className="p-tiny">
            <Typography className="text-gray-900">{name}</Typography>
            <Typography weight="bold" className="text-primary">
              {userFunction}
            </Typography>
          </div>
          <hr className="border-t border-gray-200 pb-tiny" />
          {items.map((item, index) => (
            <button
              key={`${item.name}-${index}`}
              onClick={item.onClick}
              className="flex items-center px-tiny w-full h-[4rem] text-primary hover:bg-gray-100 cursor-pointer"
            >
              <div className="text-primary flex items-center">
                {item.iconName && (
                  <span className="mr-2">
                    {getIconComponent(item.iconName)}
                  </span>
                )}
              </div>
              <Typography className="text-primary">{item.name}</Typography>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
