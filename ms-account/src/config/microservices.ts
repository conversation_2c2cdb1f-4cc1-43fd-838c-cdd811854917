import config from 'app.config.json'

import { MicroserviceClient } from '@thrift/common/engines/Http'

// Create microservice clients
export const personServiceClient = new MicroserviceClient({
  baseURL: config.microservices.person.baseUrl,
  serviceName: 'ms-person',
  timeout: 5000,
})

export const walletServiceClient = new MicroserviceClient({
  baseURL: config.microservices.wallet.baseUrl,
  serviceName: 'ms-wallet',
  timeout: 5000,
})

// Export all microservice clients
export const microserviceClients = {
  person: personServiceClient,
  wallet: walletServiceClient,
}
