import { generateRefreshToken, generateToken } from '@credentials/authorization'

import {
  ResourceMessageCode,
  type ResourceMethodProps,
} from '@thrift/common/engines/Resource'
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'
import { Post } from '@thrift/common/engines/Server'

import { Generate } from '@app/application/Authentication'
import { Language } from '@app/application/Language'

export class Authenticate {
  @Post('/authenticate')
  public async signIn({ request, response }: ResourceMethodProps) {
    try {
      const generateService = new Generate()

      const authResult = await generateService.authenticateByEmailAndPassword(
        request.body(),
      )

      const token = generateToken(authResult.accountId, authResult.tenantId)

      const refreshToken = generateRefreshToken(
        authResult.accountId,
        authResult.tenantId,
      )

      response.setHeader('Authorization', `Bearer ${token}`)
      response.setHeader('X-Refresh-Token', refreshToken)

      response.send({
        code: ResourceMessageCode.C_201_0201,
        message: Language.translate(
          `resources:${ResourceMessageCode.C_201_0201}`,
        ),
      })
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'authentication',
        Language.translate,
      )
    }
  }
}
