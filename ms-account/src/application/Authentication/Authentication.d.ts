import type { z } from 'zod'

import type {
  AuthenticateSchema,
  RefreshTokenSchema,
} from '@app/application/Authentication/Authentication.schema'

export type GenerateTokenProps = z.infer<typeof AuthenticateSchema>
export type RefreshTokenProps = z.infer<typeof RefreshTokenSchema>

export type TokenResult = {
  token: string
}

export type TokenAndRefreshResult = TokenResult & RefreshTokenProps

export type AuthenticationResult = {
  accountId: string
  tenantId: string
}

export type UserProfileResponse = {
  accountId: string
  personId: string
  email: string
  fullname: string
  roleName: string
}
