"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Server = void 0;
const express_1 = __importDefault(require("express"));
const path_1 = require("path");
const Debug_1 = require("../Debug");
const Http_1 = require("../Http");
const Resource_1 = require("../Resource");
const Server_1 = require("../Server");
const corsMiddleware = (req, res, next) => {
    res.header('Access-Control-Allow-Origin', '*');
    res.header('Access-Control-Allow-Credentials', 'true');
    res.header('Access-Control-Allow-Methods', 'GET, POST, PUT, PATCH, DELETE, OPTIONS');
    res.header('Access-Control-Allow-Headers', 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-Refresh-Token');
    res.header('Access-Control-Max-Age', '1728000');
    if (req.method === 'OPTIONS') {
        res.header('Content-Type', 'text/plain charset=UTF-8');
        res.header('Content-Length', '0');
        res.status(204).end();
        return;
    }
    next();
};
class Server {
    static get router() {
        return Object.values(Server_1.ServerMethod).reduce((result, method) => (Object.assign(Object.assign({}, result), { [method]: ({ endpoint, callback }) => Server.serverRouter[method](endpoint, (request, response) => callback({
                request: (0, Resource_1.prepareRequest)(request),
                response: (0, Resource_1.prepareResponse)(response),
            })) })), {});
    }
    static start({ pathRoot, host, port }) {
        Debug_1.Debug.info({ message: 'Press CTRL+C to stop the server' });
        (0, Resource_1.triggerResources)((0, path_1.resolve)(pathRoot, './src/resources'));
        Server.serverApp.use([
            corsMiddleware,
            express_1.default.json(),
            Http_1.requestContextMiddleware,
            (0, Resource_1.registerCredentials)((0, path_1.resolve)(pathRoot, './.credentials')),
            Server.serverRouter,
        ]);
        Server.serverApp.disable('x-powered-by');
        Server.serverApp.listen(port, (error) => {
            if (error) {
                Debug_1.Debug.error({ message: error.message });
            }
            else {
                Debug_1.Debug.info({
                    message: `The server is running on http://${host}:${port}`,
                });
            }
        });
    }
}
exports.Server = Server;
Server.serverApp = (0, express_1.default)();
Server.serverRouter = express_1.default.Router();
//# sourceMappingURL=Server.class.js.map