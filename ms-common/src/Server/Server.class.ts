import express from 'express'
import { resolve } from 'path'

import { Debug } from '@thrift/common/engines/Debug'
import { requestContextMiddleware } from '@thrift/common/engines/Http'
import {
  prepareRequest,
  prepareResponse,
  registerCredentials,
  triggerResources,
} from '@thrift/common/engines/Resource'
import {
  ServerMethod,
  type ServerRouterMethodProps,
  type ServerRouterMethods,
  type ServerStartMethodProps,
} from '@thrift/common/engines/Server'

const corsMiddleware = (
  req: express.Request,
  res: express.Response,
  next: express.NextFunction,
): void => {
  res.header('Access-Control-Allow-Origin', '*')
  res.header('Access-Control-Allow-Credentials', 'true')
  res.header(
    'Access-Control-Allow-Methods',
    'GET, POST, PUT, PATCH, DELETE, OPTIONS',
  )

  res.header(
    'Access-Control-Allow-Headers',
    'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization,X-Refresh-Token',
  )

  res.header('Access-Control-Max-Age', '1728000')

  if (req.method === 'OPTIONS') {
    res.header('Content-Type', 'text/plain charset=UTF-8')
    res.header('Content-Length', '0')

    res.status(204).end()

    return
  }

  next()
}

export class Server {
  private static readonly serverApp: express.Express = express()
  private static readonly serverRouter: express.Router = express.Router()

  static get router(): ServerRouterMethods {
    return Object.values(ServerMethod).reduce(
      (result: ServerRouterMethods, method) => ({
        ...result,
        [method]: ({ endpoint, callback }: ServerRouterMethodProps) =>
          Server.serverRouter[method](
            endpoint,
            (request: express.Request, response: express.Response) =>
              callback({
                request: prepareRequest(request),
                response: prepareResponse(response),
              }),
          ),
      }),
      {} as ServerRouterMethods,
    )
  }

  public static start({ pathRoot, host, port }: ServerStartMethodProps): void {
    Debug.info({ message: 'Press CTRL+C to stop the server' })

    triggerResources(resolve(pathRoot, './src/resources'))

    Server.serverApp.use([
      corsMiddleware,
      express.json(),
      requestContextMiddleware,
      registerCredentials(resolve(pathRoot, './.credentials')),
      Server.serverRouter,
    ])

    Server.serverApp.disable('x-powered-by')
    Server.serverApp.listen(port, (error: Error | undefined) => {
      if (error) {
        Debug.error({ message: error.message })
      } else {
        Debug.info({
          message: `The server is running on http://${host}:${port}`,
        })
      }
    })
  }
}
