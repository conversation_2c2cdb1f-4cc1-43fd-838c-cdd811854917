{"info": {"_postman_id": "", "name": "Wallet", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "838766", "_collection_link": "https://thrift-5351.postman.co/workspace/Thrift~1923e171-4c70-4695-8154-a7fbf902f8ac/collection/838766-da2ed371-68eb-4513-a5b0-d3f12a2bfb03?action=share&source=collection_link&creator=838766"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8084", "type": "string"}], "item": [{"name": "Tenant", "item": [{"name": "/tenants/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenants/:id", "host": ["{{baseUrl}}"], "path": ["tenants", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/tenants", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenants", "host": ["{{baseUrl}}"], "path": ["tenants"]}}, "response": []}, {"name": "/tenants/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenants/:id", "host": ["{{baseUrl}}"], "path": ["tenants", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/tenants", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenants?id={{id}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["tenants"], "query": [{"key": "id", "value": "{{id}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/tenants", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/tenants", "host": ["{{baseUrl}}"], "path": ["tenants"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Tenant\",\n    \"tenantUid\": \"example-tenant\",\n    \"balance\": 1000.00\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/tenants/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/tenants/:id", "host": ["{{baseUrl}}"], "path": ["tenants", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Tenant\",\n    \"tenantUid\": \"updated-tenant\",\n    \"balance\": 2000.00\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/tenants/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/tenants/:id", "host": ["{{baseUrl}}"], "path": ["tenants", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}, {"name": "Tenant Contact", "item": [{"name": "/tenant-contacts/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts/:id", "host": ["{{baseUrl}}"], "path": ["tenant-contacts", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-contacts", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts", "host": ["{{baseUrl}}"], "path": ["tenant-contacts"]}}, "response": []}, {"name": "/tenant-contacts/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts/:id", "host": ["{{baseUrl}}"], "path": ["tenant-contacts", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-contacts", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts?id={{id}}&tenantId={{tenantId}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["tenant-contacts"], "query": [{"key": "id", "value": "{{id}}"}, {"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/tenant-contacts", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts", "host": ["{{baseUrl}}"], "path": ["tenant-contacts"]}, "body": {"mode": "raw", "raw": "{\n    \"tenantId\": \"{{tenantId}}\",\n    \"type\": \"email\",\n    \"value\": \"<EMAIL>\",\n    \"isPrimary\": true\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/tenant-contacts/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts/:id", "host": ["{{baseUrl}}"], "path": ["tenant-contacts", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"type\": \"email\",\n    \"value\": \"<EMAIL>\",\n    \"isPrimary\": true\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/tenant-contacts/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/tenant-contacts/:id", "host": ["{{baseUrl}}"], "path": ["tenant-contacts", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}, {"name": "Tenant Document", "item": [{"name": "/tenant-documents/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents/:id", "host": ["{{baseUrl}}"], "path": ["tenant-documents", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-documents", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents", "host": ["{{baseUrl}}"], "path": ["tenant-documents"]}}, "response": []}, {"name": "/tenant-documents/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents/:id", "host": ["{{baseUrl}}"], "path": ["tenant-documents", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/tenant-documents", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents?id={{id}}&tenantId={{tenantId}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["tenant-documents"], "query": [{"key": "id", "value": "{{id}}"}, {"key": "tenantId", "value": "{{tenantId}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/tenant-documents", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents", "host": ["{{baseUrl}}"], "path": ["tenant-documents"]}, "body": {"mode": "raw", "raw": "{\n    \"tenantId\": \"{{tenantId}}\",\n    \"type\": \"CNPJ\",\n    \"value\": \"12345678000190\",\n    \"issuedAt\": \"2020-01-01T00:00:00.000Z\",\n    \"isPrimary\": true\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/tenant-documents/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents/:id", "host": ["{{baseUrl}}"], "path": ["tenant-documents", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"type\": \"CNPJ\",\n    \"value\": \"98765432000190\",\n    \"issuedAt\": \"2020-01-01T00:00:00.000Z\",\n    \"expiresAt\": \"2025-01-01T00:00:00.000Z\",\n    \"isPrimary\": true\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/tenant-documents/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/tenant-documents/:id", "host": ["{{baseUrl}}"], "path": ["tenant-documents", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}, {"name": "Domain", "item": [{"name": "/domains/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/domains/:id", "host": ["{{baseUrl}}"], "path": ["domains", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/domains", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/domains", "host": ["{{baseUrl}}"], "path": ["domains"]}}, "response": []}, {"name": "/domains/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/domains/:id", "host": ["{{baseUrl}}"], "path": ["domains", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/domains", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/domains", "host": ["{{baseUrl}}"], "path": ["domains"]}}, "response": []}, {"name": "/domains", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/domains", "host": ["{{baseUrl}}"], "path": ["domains"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Domain\",\n    \"tenantId\": \"uuid\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/domains/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/domains/:id", "host": ["{{baseUrl}}"], "path": ["domains", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Domain\",\n    \"tenantId\": \"uuid\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/domains/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/domains/:id", "host": ["{{baseUrl}}"], "path": ["domains", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}, {"name": "Payment Method", "item": [{"name": "/payment-methods/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods/:id", "host": ["{{baseUrl}}"], "path": ["payment-methods", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-methods", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods", "host": ["{{baseUrl}}"], "path": ["payment-methods"]}}, "response": []}, {"name": "/payment-methods/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods/:id", "host": ["{{baseUrl}}"], "path": ["payment-methods", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-methods", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods?tenantId={{tenantId}}&provider={{provider}}&enabled={{enabled}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["payment-methods"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "provider", "value": "{{provider}}"}, {"key": "enabled", "value": "{{enabled}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/payment-methods", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods", "host": ["{{baseUrl}}"], "path": ["payment-methods"]}, "body": {"mode": "raw", "raw": "{\n    \"tenantId\": \"{{tenantId}}\",\n    \"provider\": \"stripe\",\n    \"config\": {\n        \"apiKey\": \"sk_test_example\",\n        \"webhookSecret\": \"whsec_example\"\n    },\n    \"enabled\": true\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/payment-methods/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods/:id", "host": ["{{baseUrl}}"], "path": ["payment-methods", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"provider\": \"stripe\",\n    \"config\": {\n        \"apiKey\": \"sk_test_updated\",\n        \"webhookSecret\": \"whsec_updated\"\n    },\n    \"enabled\": true\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/payment-methods/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/payment-methods/:id", "host": ["{{baseUrl}}"], "path": ["payment-methods", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}, {"name": "Payment History", "item": [{"name": "/payment-history/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/payment-history/:id", "host": ["{{baseUrl}}"], "path": ["payment-history", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-history", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/payment-history", "host": ["{{baseUrl}}"], "path": ["payment-history"]}}, "response": []}, {"name": "/payment-history/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payment-history/:id", "host": ["{{baseUrl}}"], "path": ["payment-history", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/payment-history", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/payment-history?tenantId={{tenantId}}&paymentMethodId={{paymentMethodId}}&status={{status}}&fromDate={{fromDate}}&toDate={{toDate}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["payment-history"], "query": [{"key": "tenantId", "value": "{{tenantId}}"}, {"key": "paymentMethodId", "value": "{{paymentMethodId}}"}, {"key": "status", "value": "{{status}}"}, {"key": "fromDate", "value": "{{fromDate}}"}, {"key": "toDate", "value": "{{toDate}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/payment-history", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/payment-history", "host": ["{{baseUrl}}"], "path": ["payment-history"]}, "body": {"mode": "raw", "raw": "{\n    \"tenantId\": \"{{tenantId}}\",\n    \"paymentMethodId\": \"{{paymentMethodId}}\",\n    \"amount\": 99.99,\n    \"currency\": \"USD\",\n    \"status\": \"completed\",\n    \"externalId\": \"ch_123456789\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/payment-history/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/payment-history/:id", "host": ["{{baseUrl}}"], "path": ["payment-history", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"paymentMethodId\": \"{{paymentMethodId}}\",\n    \"amount\": 149.99,\n    \"currency\": \"USD\",\n    \"status\": \"refunded\",\n    \"externalId\": \"ch_updated_123456789\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/payment-history/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/payment-history/:id", "host": ["{{baseUrl}}"], "path": ["payment-history", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}