{"info": {"_postman_id": "", "name": "Stock", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "838766", "_collection_link": "https://thrift-5351.postman.co/workspace/Thrift~1923e171-4c70-4695-8154-a7fbf902f8ac/collection/838766-stock-collection"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8083", "type": "string"}], "item": [{"name": "Stock", "item": [{"name": "/stocks/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/stocks/:id", "host": ["{{baseUrl}}"], "path": ["stocks", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/stocks", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/stocks", "host": ["{{baseUrl}}"], "path": ["stocks"]}}, "response": []}, {"name": "/stocks/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stocks/:id", "host": ["{{baseUrl}}"], "path": ["stocks", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/stocks", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stocks?id={{id}}&page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["stocks"], "query": [{"key": "id", "value": "{{id}}"}, {"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/stocks", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/stocks", "host": ["{{baseUrl}}"], "path": ["stocks"]}, "body": {"mode": "raw", "raw": "{\n    \"productId\": \"product-id\",\n    \"locationId\": \"location-id\",\n    \"quantity\": 100\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/stocks/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/stocks/:id", "host": ["{{baseUrl}}"], "path": ["stocks", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"productId\": \"product-id\",\n    \"locationId\": \"location-id\",\n    \"quantity\": 150\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/stocks/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/stocks/:id", "host": ["{{baseUrl}}"], "path": ["stocks", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}, {"name": "Product", "item": [{"name": "/product/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/product/:id", "host": ["{{baseUrl}}"], "path": ["product", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/product", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/product", "host": ["{{baseUrl}}"], "path": ["product"]}}, "response": []}, {"name": "/product/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product/:id", "host": ["{{baseUrl}}"], "path": ["product", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/product", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["product"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/product", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/product", "host": ["{{baseUrl}}"], "path": ["product"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Product\",\n    \"description\": \"Product description\",\n    \"categoryId\": \"category-id\",\n    \"sku\": \"PROD-001\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/product/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/product/:id", "host": ["{{baseUrl}}"], "path": ["product", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Product\",\n    \"description\": \"Updated description\",\n    \"categoryId\": \"category-id\",\n    \"sku\": \"PROD-001\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/product/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/product/:id", "host": ["{{baseUrl}}"], "path": ["product", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}, {"name": "Product Category", "item": [{"name": "/product-categories/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/product-categories/:id", "host": ["{{baseUrl}}"], "path": ["product-categories", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/product-categories", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/product-categories", "host": ["{{baseUrl}}"], "path": ["product-categories"]}}, "response": []}, {"name": "/product-categories/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-categories/:id", "host": ["{{baseUrl}}"], "path": ["product-categories", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/product-categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-categories?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["product-categories"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/product-categories", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/product-categories", "host": ["{{baseUrl}}"], "path": ["product-categories"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Category\",\n    \"description\": \"Category description\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/product-categories/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/product-categories/:id", "host": ["{{baseUrl}}"], "path": ["product-categories", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Category\",\n    \"description\": \"Updated description\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/product-categories/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/product-categories/:id", "host": ["{{baseUrl}}"], "path": ["product-categories", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}, {"name": "Stock Location", "item": [{"name": "/stock-locations/:id", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations/:id", "host": ["{{baseUrl}}"], "path": ["stock-locations", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/stock-locations", "request": {"method": "OPTIONS", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations", "host": ["{{baseUrl}}"], "path": ["stock-locations"]}}, "response": []}, {"name": "/stock-locations/:id", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations/:id", "host": ["{{baseUrl}}"], "path": ["stock-locations", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}, {"name": "/stock-locations", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations?page=1&perPage=10", "host": ["{{baseUrl}}"], "path": ["stock-locations"], "query": [{"key": "page", "value": "1"}, {"key": "perPage", "value": "10"}]}}, "response": []}, {"name": "/stock-locations", "request": {"method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations", "host": ["{{baseUrl}}"], "path": ["stock-locations"]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Example Location\",\n    \"address\": \"123 Warehouse St\",\n    \"type\": \"warehouse\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/stock-locations/:id", "request": {"method": "PUT", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations/:id", "host": ["{{baseUrl}}"], "path": ["stock-locations", ":id"], "variable": [{"key": "id", "value": ""}]}, "body": {"mode": "raw", "raw": "{\n    \"name\": \"Updated Location\",\n    \"address\": \"456 Storage Ave\",\n    \"type\": \"warehouse\"\n}", "options": {"raw": {"language": "json"}}}}, "response": []}, {"name": "/stock-locations/:id", "request": {"method": "DELETE", "header": [], "url": {"raw": "{{baseUrl}}/stock-locations/:id", "host": ["{{baseUrl}}"], "path": ["stock-locations", ":id"], "variable": [{"key": "id", "value": ""}]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}]}