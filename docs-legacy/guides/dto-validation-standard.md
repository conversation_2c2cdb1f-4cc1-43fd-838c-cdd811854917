# Standardized DTOs and Zod Validation Guide

This guide provides step-by-step instructions for implementing application-layer DTOs and Zod validation across all microservices in the Thrift Technology platform.

## Table of Contents

1. [Overview](#overview)
2. [Directory Structure](#directory-structure)
3. [Implementation Steps](#implementation-steps)
   - [Define DTOs and Zod Schemas](#1-define-dtos-and-zod-schemas)
   - [Implement Application Layer with Validation](#2-implement-application-layer-with-validation)
   - [Implement Resource Layer with Error Handling](#3-implement-resource-layer-with-error-handling)
4. [Best Practices](#best-practices)
5. [Templates](#templates)
6. [Migration Guide](#migration-guide)
7. [Example Implementation](#example-implementation)

## Overview

The standardized approach follows these principles:

1. **DTOs and Schemas in Application Layer**: Define all DTOs and Zod schemas in the application layer
2. **Validation in Application Layer**: Perform validation in the application layer using Zod
3. **Standardized Error Handling**: Use ResponseHelper.handleError() for consistent error handling
4. **Clean Architecture**: Maintain separation of concerns between layers

## Directory Structure

```
ms-<service>/
└── src/
    ├── application/
    │   └── Entity/
    │       ├── Entity.schema.ts    # DTOs and Zod schemas
    │       ├── Entity.d.ts         # Type definitions (optional if using Zod inference)
    │       └── Entity.class.ts     # Application logic with validation
    ├── domain/
    │   └── database/
    │       └── Entity/
    │           └── Entity.class.ts # Data access logic
    └── resources/
        └── Entity/
            └── Entity.resource.ts  # API endpoints with error handling
```

## Implementation Steps

### 1. Define DTOs and Zod Schemas

Create a schema file in the application layer:

```typescript
// src/application/Entity/Entity.schema.ts
import { z } from 'zod'

// Base schema with common properties
export const EntityBaseSchema = z.object({
  name: z.string().min(3),
  description: z.string().optional(),
  // Add more properties as needed
})

// Schema for creating a new entity
export const CreateEntitySchema = EntityBaseSchema

// Schema for updating an existing entity
export const UpdateEntitySchema = EntityBaseSchema.extend({
  id: z.string().uuid(),
}).partial({
  // Make all base properties optional for updates
  name: true,
  description: true,
})

// Schema for filtering entities
export const FilterEntitySchema = z.object({
  id: z.string().uuid().optional(),
  search: z.string().optional(),
})

// Type definitions using Zod inference
export type CreateEntityDto = z.infer<typeof CreateEntitySchema>
export type UpdateEntityDto = z.infer<typeof UpdateEntitySchema>
export type FilterEntityDto = z.infer<typeof FilterEntitySchema>
```

### 2. Implement Application Layer with Validation

Create application classes that use the schemas for validation:

```typescript
// src/application/Entity/Create.class.ts
import { validateWithZod } from '@thrift/common/engines/Validation/validateWithZod'
import { CreateEntitySchema } from './Entity.schema'
import type { CreateEntityDto } from './Entity.schema'

export class Create {
  public async create(input: unknown) {
    // Validate input using Zod schema
    const dto: CreateEntityDto = validateWithZod(CreateEntitySchema, input)
    
    // Additional validation or business logic here
    
    // Call domain layer
    return this.entityDomain.create(dto)
  }
}
```

```typescript
// src/application/Entity/Update.class.ts
import { validateWithZod } from '@thrift/common/engines/Validation/validateWithZod'
import { UpdateEntitySchema } from './Entity.schema'
import type { UpdateEntityDto } from './Entity.schema'

export class Update {
  public async update(input: unknown) {
    // Validate input using Zod schema
    const dto: UpdateEntityDto = validateWithZod(UpdateEntitySchema, input)
    
    // Additional validation or business logic here
    
    // Call domain layer
    return this.entityDomain.update(dto)
  }
}
```

### 3. Implement Resource Layer with Error Handling

Create resource classes that use ResponseHelper for error handling:

```typescript
// src/resources/Entity/Entity.resource.ts
import { ResponseHelper } from '@thrift/common/engines/Resource/helpers/ResponseHelper'

export class EntityResource {
  @Post('/entities')
  public async postEntity({ request, response }: ResourceMethodProps) {
    try {
      // Pass raw request body to application layer for validation
      const data = await new Create().create(request.body())
      
      ResponseHelper.sendSuccessResponse(
        response,
        data,
        ResourceMessageCode.C_201_0001,
        Language.translate,
        { title: 'entity' },
      )
    } catch (error) {
      ResponseHelper.handleError(
        error,
        response,
        'entity',
        Language.translate,
      )
    }
  }
}
```

## Best Practices

1. **Keep Validation in Application Layer**: Never validate in the resource layer
2. **Use Zod Inference for Types**: Define types using `z.infer<typeof Schema>`
3. **Standardized Error Handling**: Always use ResponseHelper.handleError()
4. **Consistent Naming**: Follow the naming conventions in this guide
5. **Reuse Base Schemas**: Create base schemas for common properties
6. **Make Update Fields Optional**: Use `.partial()` for update schemas

## Templates

Reference templates are available in `ms-common/src/Resource/templates/`:

- `EntitySchema.template.ts`: Template for DTOs and schemas
- `EntityApplication.template.ts`: Template for application classes
- `EntityResource.template.ts`: Template for resource classes

## Migration Guide

When migrating existing code:

1. Move any validation from resource layer to application layer
2. Replace class-validator with Zod schemas
3. Update error handling to use ResponseHelper.handleError()
4. Ensure consistent naming across all layers

## Example Implementation

See the Tenant entity in ms-wallet for a complete example:

- `ms-wallet/src/application/Tenant/Tenant.schema.ts`
- `ms-wallet/src/application/Tenant/Tenant.d.ts`
- `ms-wallet/src/application/Tenant/Create.class.ts`
- `ms-wallet/src/application/Tenant/Update.class.ts`
- `ms-wallet/src/application/Tenant/Read.class.ts`
- `ms-wallet/src/resources/Tenant/Tenant.resource.ts`
