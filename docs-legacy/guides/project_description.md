# Thrift Technology Project Description

## Project Overview
Thrift Technology is a microservices-based platform built with modern TypeScript and Node.js technologies. The system follows Clean Architecture principles and consists of multiple microservices that handle different aspects of the business domain, including account management, person data, wallet functionality, and stock management.

## Architecture

### Clean Architecture Implementation
The project strictly follows Clean Architecture principles with three main layers:
- **Domain Layer**: Represents Enterprise Business Rules and abstracts all external dependencies
- **Application Layer**: Implements Application Business Rules and serves as middleware between domain and resources
- **Resources Layer**: Handles REST/RESTful resources and interface adapters

### Microservices
The platform consists of the following microservices:
- **ms-account**: Handles user accounts and authentication
- **ms-person**: Manages person data (natural and legal persons)
- **ms-wallet**: Manages financial transactions, payment methods, and wallet functionality
- **ms-stock**: Handles inventory and stock management
- **ms-common**: Shared library used by all microservices
- **design-system**: UI component library for frontend applications

### Frontend
- **mf-home**: React-based frontend application
- **design-system**: Shared UI component library built with React, Tailwind CSS, and Storybook

## Technology Stack

### Backend
- **Language**: TypeScript (Node.js v20-22)
- **Runtime**: Node.js (v20-24)
- **Package Manager**: Yarn (v1.22)
- **Module System**: ES Modules (most services) with some CommonJS compatibility
- **Database**: MySQL 8 with Prisma ORM
- **API**: RESTful services with Express
- **Containerization**: Docker and Docker Compose

### Frontend
- **Framework**: React 19
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **State Management**: React Query (TanStack Query)
- **Form Handling**: React Hook Form with Zod validation
- **Component Documentation**: Storybook

### Development Tools
- **Linting**: ESLint with strict configuration
- **Formatting**: Prettier
- **Type Checking**: TypeScript
- **Development Server**: tsx watch
- **Build**: tsc with tsc-alias

## Coding Standards and Best Practices

### Architecture Guidelines
1. Follow Clean Architecture principles strictly
2. Keep layers separated with proper dependency direction
3. Domain layer should not depend on application or resources layers
4. Application layer should not depend on resources layer
5. Use dependency injection for external dependencies

### Code Quality
1. Maximum file size: 200 lines
2. Maximum function complexity: 10 (enforced by ESLint)
3. Maximum nesting depth: 2 levels
4. Use proper typing with TypeScript
5. Avoid any explicit `any` types
6. Use camelCase for variables, PascalCase for classes and types
7. Keep validations only in the application layer
8. Use Decimal type for financial amounts

### Error Handling
1. Use ResponseHelper.handleError() in resource classes for standardized error handling
2. Avoid implementing error handling directly in resources
3. Proper error propagation through layers

### Inter-service Communication
1. Use the MicroserviceClient from @thrift/common for service-to-service communication
2. Pass bearer tokens between services for authentication
3. Configure axios in ms-common for shared functionality

### Database
1. Use Prisma ORM for database access
2. Follow proper schema design with relations
3. Use Decimal type for amount fields to avoid floating-point issues
4. Ensure proper tenant ID linking across related DTOs

## Development Workflow

### Environment Setup
1. Clone all required microservice repositories alongside the base repository
2. Use make commands to run operations across repositories
3. Directory structure should maintain all services at the same level

### Running Services
1. Use `yarn dev` to start services in development mode
2. Services run on different ports to avoid conflicts
3. MySQL instances run on different ports for each service

### Docker Workflow
1. Use docker-compose for development environment
2. Each service has its own database container
3. Use provisioning scripts to set up infrastructure

## Things to Avoid

1. Don't mix layers (domain, application, resources) - respect the architecture
2. Avoid unnecessary comments in the code
3. Don't use console.log - use the Debug engine instead
4. Don't exceed the maximum complexity of 10
5. Don't exceed the maximum file size of 200 lines
6. Don't use explicit `any` types
7. Don't implement error handling directly in resources - use ResponseHelper.handleError()
8. Don't use floating-point types for financial calculations - use Decimal
9. Don't commit code that doesn't pass linting and formatting checks
10. Don't create circular dependencies between services

## Best Approaches

1. Use the helper ResponseHelper.handleError() method in resource classes
2. Keep validations simple and only in the application layer
3. Limit filtering to IDs and search text
4. Use Decimal type for amount fields in the wallet app
5. When implementing CRUD operations, ensure proper linking of tenant IDs
6. Pass bearer tokens between services for authentication
7. Use the @thrift/common package for shared functionality
8. Follow the eslint rules in "@ms-common/.eslint.config/default/config.js"
9. Use shell scripts for common development tasks
10. Implement proper error handling and validation

## Project Structure
Each microservice follows a similar structure:
```
/ms-service
  /.scripts         # Shell scripts for common tasks
  /.storage         # Storage for development (database, etc.)
  /prisma           # Prisma schema and migrations
  /src
    /application    # Application business rules
    /domain         # Enterprise business rules
      /database     # Database models and repositories
    /resources      # REST resources and controllers
    main.ts         # Entry point
  app.config.json   # Configuration
  docker-compose.yml # Docker configuration
  package.json      # Dependencies and scripts
```

## Deployment and Infrastructure
The services are containerized using Docker and can be deployed using Docker Compose or Kubernetes. Each service has its own database instance and communicates with other services via HTTP APIs.
