// CONSOLIDATED SCHEMA FROM ALL MICROSERVICES
// This file combines schemas from: ms-account, ms-person, ms-wallet, ms-stock, ms-purchase, ms-evaluation

generator client {
  provider        = "prisma-client-js"
  previewFeatures = []
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// ==================== MS-ACCOUNT ====================
model Acl {
  id          String   @id @default(uuid())
  name        String
  tagname     String   @unique
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deleted     Boolean  @default(false)

  roles Role[]

  @@map("acl")
}

model Role {
  id          String   @id @default(uuid())
  name        String
  description String?
  shortname   String   @unique
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deleted     Boolean  @default(false)

  acls     Acl[]
  accounts Account[]

  @@map("roles")
}

model Account {
  id                String    @id @default(uuid())
  username          String    @unique
  password          String
  passwordExpiresAt DateTime?
  isActive          Boolean   @default(true)
  personId          String?
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  deleted           Boolean   @default(false)

  roleId String?
  role   Role?   @relation(fields: [roleId], references: [id])

  @@map("accounts")
}

// ==================== MS-PERSON ====================
model Person {
  id            String         @id @default(uuid())
  name          String
  createdAt     DateTime       @default(now())
  updatedAt     DateTime       @updatedAt
  deleted       Boolean        @default(false)
  PersonNatural PersonNatural?
  PersonLegal   PersonLegal?
  Document      Document?
  Address       Address?
  Contact       Contact?

  @@map("persons")
}

model PersonNatural {
  id        String   @id @default(uuid())
  person    Person   @relation(fields: [personId], references: [id])
  personId  String   @unique
  name      String
  gender    String
  birthDate DateTime
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@map("person_naturals")
}

model PersonLegal {
  id             String   @id @default(uuid())
  person         Person   @relation(fields: [personId], references: [id])
  personId       String   @unique
  fictitiousName String
  legalName      String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt
  deleted        Boolean  @default(false)

  @@map("person_legals")
}

model Document {
  id        String   @id @default(uuid())
  person    Person   @relation(fields: [personId], references: [id])
  personId  String   @unique
  type      String
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@map("documents")
}

model Address {
  id         String   @id @default(uuid())
  person     Person   @relation(fields: [personId], references: [id])
  personId   String   @unique
  line1      String
  number     String
  line2      String
  city       String
  region     String
  country    String
  postalCode String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  deleted    Boolean  @default(false)

  @@map("addresses")
}

model Contact {
  id        String   @id @default(uuid())
  person    Person   @relation(fields: [personId], references: [id])
  personId  String   @unique
  label     String
  type      String
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  @@map("contacts")
}

// ==================== MS-WALLET ====================
model Tenant {
  id          String   @id @default(uuid())
  name        String
  tenantUid   String   @unique
  balance     Decimal  @default(0)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deleted     Boolean  @default(false)

  contacts       TenantContact[]
  documents      TenantDocument[]
  domains        Domain[]
  paymentMethods PaymentMethod[]
  paymentHistory PaymentHistory[]

  @@map("tenants")
}

model TenantContact {
  id        String      @id @default(uuid())
  tenant    Tenant      @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId  String
  type      String
  value     String
  isPrimary Boolean     @default(false)
  createdAt DateTime    @default(now())
  updatedAt DateTime    @updatedAt
  deleted   Boolean     @default(false)

  @@unique([tenantId, type, value])
  @@map("tenant_contacts")
}

model TenantDocument {
  id        String    @id @default(uuid())
  tenant    Tenant    @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId  String
  type      String
  value     String
  issuedAt  DateTime?
  expiresAt DateTime?
  isPrimary Boolean   @default(false)
  createdAt DateTime  @default(now())
  updatedAt DateTime  @updatedAt
  deleted   Boolean   @default(false)

  @@unique([tenantId, type, value])
  @@map("tenant_documents")
}

model Domain {
  id        String     @id @default(uuid())
  tenant    Tenant     @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId  String
  name      String
  isPrimary Boolean    @default(false)
  createdAt DateTime   @default(now())
  updatedAt DateTime   @updatedAt
  deleted   Boolean    @default(false)

  @@unique([tenantId, name])
  @@map("domains")
}

model PaymentMethod {
  id        String   @id @default(uuid())
  tenant    Tenant   @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId  String
  provider  String
  config    Json
  enabled   Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  deleted   Boolean  @default(false)

  paymentHistory PaymentHistory[]

  @@map("payment_methods")
}

model PaymentHistory {
  id              String        @id @default(uuid())
  tenant          Tenant        @relation(fields: [tenantId], references: [id], onDelete: Cascade)
  tenantId        String
  paymentMethod   PaymentMethod @relation(fields: [paymentMethodId], references: [id])
  paymentMethodId String
  amount          Decimal
  currency        String
  status          String
  externalId      String?
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt
  deleted         Boolean       @default(false)

  @@map("payment_history")
}

// ==================== MS-STOCK ====================
model ProductCategory {
  id          String   @id @default(uuid())
  name        String
  description String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deleted     Boolean  @default(false)

  productCategoryMeasurements ProductCategoryMeasurements[]
  products                    Product[]

  @@map("product_categories")
}

model ProductCategoryMeasurements {
  id                String          @id @default(uuid())
  productCategory   ProductCategory @relation(fields: [productCategoryId], references: [id])
  productCategoryId String
  size              String
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  deleted           Boolean         @default(false)

  @@map("product_category_measurements")
}

model Product {
  id                String          @id @default(uuid())
  name              String
  productCategory   ProductCategory @relation(fields: [productCategoryId], references: [id])
  productCategoryId String
  createdAt         DateTime        @default(now())
  updatedAt         DateTime        @updatedAt
  deleted           Boolean         @default(false)

  @@map("products")
}

model ProductClassification {
  id          String   @id @default(uuid())
  name        String
  description String
  coefficient Float
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deleted     Boolean  @default(false)

  @@map("product_classifications")
}

model StockLocation {
  id                    String          @id @default(uuid())
  parentStockLocationId String?
  parentStockLocation   StockLocation?  @relation("ParentChildStockLocation", fields: [parentStockLocationId], references: [id])
  children              StockLocation[] @relation("ParentChildStockLocation")
  description           String
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt
  deleted               Boolean         @default(false)
  stocks                Stock[]

  @@map("stock_locations")
}

model Stock {
  id                      String        @id @default(uuid())
  productId               String
  productClassificationId String
  stockLocationId         String
  stockLocation           StockLocation @relation(fields: [stockLocationId], references: [id])
  notes                   String?
  buyingPrice             Float
  expectedSellingPrice    Float
  createdAt               DateTime      @default(now())
  updatedAt               DateTime      @updatedAt
  deleted                 Boolean       @default(false)

  @@map("stock")
}

// ==================== MS-PURCHASE ====================
model Register {
  id             String        @id @default(uuid()) @map("register_id")
  storeId        String        @map("store_id")
  cashierId      String        @map("cashier_id")
  status         String        @db.VarChar(20) @default("CLOSED")
  openedAt       DateTime      @default(now()) @map("opened_at")
  closedAt       DateTime?     @map("closed_at")
  openingBalance Float         @map("opening_balance")
  closingBalance Float?        @map("closing_balance")
  transactions   Transaction[]
  metadata       Json?

  @@index([storeId], name: "store_register_index")
}

model Transaction {
  id            String   @id @default(uuid()) @map("transaction_id")
  register      Register @relation(fields: [registerId], references: [id])
  registerId    String   @map("register_id")
  orderId       String?  @map("order_id")
  amount        Float
  type          String   @db.VarChar(20)
  paymentMethod String   @db.VarChar(20)
  processedAt   DateTime @default(now()) @map("processed_at")
  status        String   @db.VarChar(20) @default("PENDING")
  metadata      Json?

  @@index([orderId], name: "order_transaction_index")
}

model ShoppingCart {
  id        String             @id @default(uuid())
  userId    String
  items     ShoppingCartItem[]
  createdAt DateTime           @default(now())
  updatedAt DateTime           @updatedAt
  status    String             @default("ACTIVE") @db.VarChar(20)
  metadata  Json?

  @@index([userId])
  @@index([status])
}

model ShoppingCartItem {
  id        String       @id @default(uuid())
  cart      ShoppingCart @relation(fields: [cartId], references: [id])
  cartId    String
  productId String
  quantity  Int          @default(1)
  addedAt   DateTime     @default(now())
  metadata  Json?

  @@index([productId])
}

// ==================== MS-EVALUATION ====================
model EvaluationBatch {
  id          String   @id @default(uuid())
  supplierId  String   // FK to Person.id from ms-person
  storeId     String   // FK to Store.id or TenantStore.id from TBD
  status      String   // PENDING_REVIEW, UNDER_EVALUATION, EVALUATION_COMPLETED, PROPOSAL_SENT, PROPOSAL_ACCEPTED, PROPOSAL_REJECTED, CLOSED
  displayCode String   // Code for totem
  notes       String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  deleted     Boolean  @default(false)

  items     EvaluationItem[]
  proposals EvaluationProposal[]

  @@map("evaluation_batches")
}

model EvaluationItem {
  id                                String          @id @default(uuid())
  evaluationBatch                   EvaluationBatch @relation(fields: [evaluationBatchId], references: [id], onDelete: Cascade)
  evaluationBatchId                 String
  supplierDescription               String?
  supplierSuggestedCategoryId       String?
  evaluatedProductCategoryId        String          // FK to ProductCategory from ms-stock
  evaluatedProductClassificationId  String          // FK to ProductClassification from ms-stock
  evaluatorNotes                    String?
  proposedBuyPrice                  Decimal         @default(0)
  status                            String          // PENDING_EVALUATION, ACCEPTED, REJECTED
  rejectionReason                   String?
  estimatedMarketValue              Decimal?
  createdAt                         DateTime        @default(now())
  updatedAt                         DateTime        @updatedAt
  deleted                           Boolean         @default(false)

  @@map("evaluation_items")
}

model EvaluationProposal {
  id                    String          @id @default(uuid())
  evaluationBatch       EvaluationBatch @relation(fields: [evaluationBatchId], references: [id], onDelete: Cascade)
  evaluationBatchId     String
  totalProposedValue    Decimal         @default(0)
  proposalDate          DateTime        @default(now())
  status                String          // DRAFT, SENT_TO_SUPPLIER, ACCEPTED_BY_SUPPLIER, REJECTED_BY_SUPPLIER
  supplierResponseDate  DateTime?
  supplierNotes         String?
  createdAt             DateTime        @default(now())
  updatedAt             DateTime        @updatedAt
  deleted               Boolean         @default(false)

  @@map("evaluation_proposals")
}