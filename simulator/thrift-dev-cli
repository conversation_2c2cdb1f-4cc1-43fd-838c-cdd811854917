#!/bin/bash
set -eux

# Define variables
_root_path="$(cd "$(dirname "${BASH_SOURCE[0]}")/./" && pwd)"

# Helper functions
function _build_service() {
  local service_name=$1
  echo "Building service: $service_name"
  
  docker compose \
    --project-name "thrift" \
    --file ${_root_path}/docker/macro-services/macro-service-docker-compose.yml \
    run --rm --no-TTY \
    --name "${service_name}-build" \
    --env APP_NAME=${service_name} \
    macro-service-build < /dev/null
}

function _build_frontend() {
  local frontend_name=$1
  echo "Building frontend: $frontend_name"
  
  docker compose \
    --project-name "thrift" \
    --file ${_root_path}/docker/macro-frontends/macro-frontend-docker-compose.yml \
    run --rm --no-TTY \
    --name "${frontend_name}-build" \
    --env APP_NAME=${frontend_name} \
    macro-frontend-build < /dev/null
}

function _restart_service() {
  local service_name=$1
  echo "Restarting service: $service_name"

  # Stop the service
  docker stop "${service_name}-runtime" 2>/dev/null || true
  docker rm "${service_name}-runtime" 2>/dev/null || true

  # Start the service
  local service_dir="${_root_path}/.cache/apps/${service_name}"
  local server_port=$(grep -m 1 '"port":' "${service_dir}/dist/app.config.json" | sed -E 's/.*"port": ([0-9]+).*/\1/')

  docker compose \
    --project-name "thrift" \
    --file ${_root_path}/docker/macro-services/macro-service-docker-compose.yml \
    run --rm --detach --no-TTY \
    --volume "${service_dir}:/usr/src/app" \
    --name "${service_name}-runtime" \
    --publish "${server_port}:${server_port}" \
    macro-service-runtime < /dev/null
}

function _debug_service() {
  local service_name=$1
  echo "Starting service in debug mode: $service_name"

  # Stop existing service
  docker stop "${service_name}-runtime" 2>/dev/null || true
  docker rm "${service_name}-runtime" 2>/dev/null || true

  local service_dir="${_root_path}/.cache/apps/${service_name}"
  local server_port=$(grep -m 1 '"port":' "${service_dir}/dist/app.config.json" | sed -E 's/.*"port": ([0-9]+).*/\1/')
  local debug_port=$((9229 + ${server_port: -1}))  # Unique debug port per service

  echo "Service will be available at: http://localhost:${server_port}"
  echo "Debug port: ${debug_port}"
  echo "Chrome DevTools: chrome://inspect"

  docker compose \
    --project-name "thrift" \
    --file ${_root_path}/docker/macro-services/macro-service-debug-docker-compose.yml \
    run --rm --no-TTY \
    --volume "${service_dir}:/usr/src/app" \
    --name "${service_name}-debug" \
    --publish "${server_port}:${server_port}" \
    --publish "${debug_port}:9229" \
    --env SERVICE_NAME=${service_name} \
    macro-service-debug
}

function _dev_service() {
  local service_name=$1
  echo "Starting service in development mode: $service_name"

  # This assumes the service has a local copy for development
  if [ ! -d "${service_name}" ]; then
    echo "Error: ${service_name} directory not found. Clone the repository first."
    exit 1
  fi

  cd "${service_name}"
  echo "Starting development server for ${service_name}..."
  echo "Press Ctrl+C to stop"
  yarn dev
}

function _quick_setup() {
  echo "Quick setup - only database and essential services"
  /bin/bash ${_root_path}/cli/database --up
}

function _dev_mode() {
  echo "Starting development mode..."
  _quick_setup
  
  # Build only essential services
  _build_service "ms-account"
  _build_service "ms-person"
  
  # Start services
  /bin/bash ${_root_path}/cli/macro-service --up
  /bin/bash ${_root_path}/cli/api-gateway --build
  /bin/bash ${_root_path}/cli/api-gateway --up
  /bin/bash ${_root_path}/cli/cdn --up
}

# Process command line arguments
for arg in "$@"; do
  case "$arg" in
    --quick-setup)
      _quick_setup
      ;;
      
    --dev)
      _dev_mode
      ;;
      
    --build-service)
      shift
      _build_service "$1"
      ;;
      
    --build-frontend)
      shift
      _build_frontend "$1"
      ;;
      
    --restart-service)
      shift
      _restart_service "$1"
      ;;
      
    --rebuild-service)
      shift
      _build_service "$1"
      _restart_service "$1"
      ;;
      
    --status)
      echo "=== Docker Containers Status ==="
      docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
      ;;
      
    --debug-service)
      shift
      _debug_service "$1"
      ;;

    --dev-service)
      shift
      _dev_service "$1"
      ;;

    --logs)
      shift
      docker logs -f "$1-runtime"
      ;;

    --help)
      echo "Thrift Development CLI"
      echo ""
      echo "Usage: ./thrift-dev-cli [OPTION]"
      echo ""
      echo "Options:"
      echo "  --quick-setup           Start only database"
      echo "  --dev                   Development mode (essential services only)"
      echo "  --build-service NAME    Build specific service"
      echo "  --build-frontend NAME   Build specific frontend"
      echo "  --restart-service NAME  Restart specific service"
      echo "  --rebuild-service NAME  Rebuild and restart service"
      echo "  --debug-service NAME    Start service in debug mode"
      echo "  --dev-service NAME      Start service in development mode (local)"
      echo "  --status               Show container status"
      echo "  --logs SERVICE         Show logs for service"
      echo "  --help                 Show this help"
      ;;
  esac
done

exit 0
