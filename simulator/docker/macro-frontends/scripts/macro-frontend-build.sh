#!/bin/bash
set -eux

# Service name
_storageFilename="${STORAGE_DIR}/${APP_NAME}"

# Remove cache
rm -rf ${REPOSITORY_NAME} ${_storageFilename}

# Prepare repository
git config --global url."https://$<EMAIL>/thrift-technology/".insteadOf "https://github.com/thrift-technology/"
git clone https://github.com/thrift-technology/${APP_NAME}.git ${REPOSITORY_NAME}
cp "${REPOSITORY_NAME}/app.config.sample.json" "${REPOSITORY_NAME}/app.config.json"

# Build application
cd ${REPOSITORY_NAME}
yarn cache clean
yarn install --immutable || yarn install --no-immutable
yarn build
cd ..

# Copy files to service directory
_builtDir=$(node -e "console.log(require('./${REPOSITORY_NAME}/app.config.json').build.outDir)")
mkdir -p ${_storageFilename}
cp -r ${REPOSITORY_NAME}/${_builtDir} ${_storageFilename}/${_builtDir}
cp -r ${REPOSITORY_NAME}/app.config.json ${_storageFilename}/app.config.json

exit 0
