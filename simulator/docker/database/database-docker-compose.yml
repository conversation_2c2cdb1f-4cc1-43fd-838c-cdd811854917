name: thrift
services:
  database-runtime:
    container_name: database-runtime
    image: mysql:8
    restart: always
    user: "${UID:-1000}:${GID:-1000}"
    env_file:
      - ../variables/database.env
    environment:
      - TZ=UTC
      - MYSQL_USER=mysql
      - MYSQL_UID=${UID:-1000}
      - MYSQL_GID=${GID:-1000}
    volumes:
      - ../../.cache/database/mysql:/var/lib/mysql
    expose:
      - "3306"
    ports:
      - "3306:3306"
    healthcheck:
      test:
        [
          "CMD",
          "mysqladmin",
          "ping",
          "-h",
          "localhost",
          "-u",
          "root",
          "-p${DATABASE_PASSWORD:-root}",
        ]
      interval: 10s
      timeout: 5s
      retries: 3

  database-cleanup:
    container_name: database-cleanup
    image: node:22-alpine
    working_dir: /usr/src/app
    volumes:
      - ../../.cache/database:/usr/src/app
    command: rm -rf ./mysql
