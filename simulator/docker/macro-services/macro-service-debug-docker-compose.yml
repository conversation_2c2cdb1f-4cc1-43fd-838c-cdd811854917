name: thrift
services:
  macro-service-debug:
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/database.env
    environment:
      - NODE_ENV=development
      - DEBUG=*
    command: /usr/local/bin/node --inspect=0.0.0.0:9229 /usr/src/app/dist/src/main.js
    volumes:
      - ./scripts:/usr/src/scripts
    ports:
      - "9229:9229"  # Debug port
    stdin_open: true
    tty: true

  macro-service-dev:
    image: node:22
    working_dir: /usr/src/app
    env_file:
      - ../variables/database.env
    environment:
      - NODE_ENV=development
    command: yarn dev
    volumes:
      - ./scripts:/usr/src/scripts
    stdin_open: true
    tty: true
