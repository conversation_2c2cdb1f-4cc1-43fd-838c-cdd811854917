#!/bin/bash
set -eux

# Define variables
_root_path="$(cd "$(dirname "${BASH_SOURCE[0]}")/../" && pwd)"
_docker_compose_filename="${_root_path}/docker/database/database-docker-compose.yml"

function _up() {
  # Ensure the mysql data directory exists with correct permissions
  mkdir -p "${_root_path}/.cache/database/mysql"

  docker compose --file ${_docker_compose_filename} pull database-runtime < /dev/null

  # Use custom variable names to avoid readonly system variables
  export DOCKER_UID=$(id -u)
  export DOCKER_GID=$(id -g)

  docker compose \
    --file ${_docker_compose_filename} \
    up --detach --build database-runtime < /dev/null
}

function _down() {
  local _containers_to_remove=$(docker ps -a --format "{{.ID}}\t{{.Names}}" | grep -E '\sdatabase' | awk '{print $1}')

  if [ ! -z "$_containers_to_remove" ]; then
    docker compose \
      --file ${_docker_compose_filename} \
      kill database-runtime < /dev/null

    docker compose \
      --file ${_docker_compose_filename} \
      rm --force --volumes database-runtime < /dev/null
  fi
}

function _cleanup() {
  docker compose \
    --project-name "thrift" \
    --file ${_docker_compose_filename} \
    run --rm --no-TTY \
    database-cleanup < /dev/null
}

# Process command line arguments
for arg in "$@"; do
  case "$arg" in
    --up)
      _up
      ;;

    --down)
    _down
      ;;

    --cleanup)
      _down
      _cleanup
      ;;
  esac
done

exit 0
