{"version": "0.2.0", "configurations": [{"name": "Debug ms-account (Container)", "type": "node", "request": "attach", "port": 9229, "address": "localhost", "localRoot": "${workspaceFolder}/../ms-account", "remoteRoot": "/usr/src/app", "protocol": "inspector", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"name": "Debug ms-person (Container)", "type": "node", "request": "attach", "port": 9230, "address": "localhost", "localRoot": "${workspaceFolder}/../ms-person", "remoteRoot": "/usr/src/app", "protocol": "inspector", "restart": true, "skipFiles": ["<node_internals>/**"]}, {"name": "Debug ms-account (Local)", "type": "node", "request": "launch", "program": "${workspaceFolder}/../ms-account/src/main.ts", "cwd": "${workspaceFolder}/../ms-account", "runtimeArgs": ["--loader", "tsx/esm"], "env": {"NODE_ENV": "development"}, "console": "integratedTerminal", "skipFiles": ["<node_internals>/**"]}]}