#!/bin/bash
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[$(date '+%H:%M:%S')] $1${NC}"
}

print_error() {
    echo -e "${RED}[$(date '+%H:%M:%S')] ERROR: $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}[$(date '+%H:%M:%S')] WARNING: $1${NC}"
}

print_info() {
    echo -e "${BLUE}[$(date '+%H:%M:%S')] INFO: $1${NC}"
}

# Function to check if service directory exists
check_service() {
    local service_name=$1
    if [ ! -d "../${service_name}" ]; then
        print_error "Service directory ../${service_name} not found"
        print_info "Please clone the ${service_name} repository to the parent directory"
        return 1
    fi
    return 0
}

# Function to start a service in watch mode
start_service_watch() {
    local service_name=$1
    print_status "Starting ${service_name} in watch mode..."
    
    if ! check_service "$service_name"; then
        return 1
    fi
    
    cd "../${service_name}"
    
    # Check if yarn dev script exists
    if ! yarn run --silent dev --help >/dev/null 2>&1; then
        print_error "${service_name} doesn't have a 'dev' script"
        return 1
    fi
    
    print_info "Starting ${service_name} with hot reload..."
    print_info "Press Ctrl+C to stop"
    
    # Start the service in development mode
    yarn dev
}

# Function to start multiple services
start_multiple_services() {
    local services=("$@")
    
    print_status "Starting multiple services in watch mode..."
    
    # Check all services first
    for service in "${services[@]}"; do
        if ! check_service "$service"; then
            print_error "Cannot start services due to missing directories"
            exit 1
        fi
    done
    
    # Start services in background
    for service in "${services[@]}"; do
        print_status "Starting ${service}..."
        (
            cd "../${service}"
            yarn dev > "../thrift/simulator/.logs/${service}.log" 2>&1
        ) &
        
        # Store PID for cleanup
        echo $! > ".pids/${service}.pid"
    done
    
    print_status "All services started. Logs available in .logs/ directory"
    print_info "Press Ctrl+C to stop all services"
    
    # Wait for interrupt
    trap 'cleanup_services "${services[@]}"' INT
    wait
}

# Function to cleanup background services
cleanup_services() {
    local services=("$@")
    print_warning "Stopping services..."
    
    for service in "${services[@]}"; do
        if [ -f ".pids/${service}.pid" ]; then
            local pid=$(cat ".pids/${service}.pid")
            if kill -0 "$pid" 2>/dev/null; then
                print_status "Stopping ${service} (PID: ${pid})"
                kill "$pid"
            fi
            rm -f ".pids/${service}.pid"
        fi
    done
    
    print_status "All services stopped"
    exit 0
}

# Create necessary directories
mkdir -p .logs .pids

# Main script logic
case "${1:-help}" in
    "ms-account")
        start_service_watch "ms-account"
        ;;
    "ms-person")
        start_service_watch "ms-person"
        ;;
    "ms-wallet")
        start_service_watch "ms-wallet"
        ;;
    "ms-evaluation")
        start_service_watch "ms-evaluation"
        ;;
    "ms-stock")
        start_service_watch "ms-stock"
        ;;
    "ms-purchase")
        start_service_watch "ms-purchase"
        ;;
    "mf-home")
        start_service_watch "mf-home"
        ;;
    "all-services")
        start_multiple_services "ms-account" "ms-person" "ms-wallet"
        ;;
    "essential")
        start_multiple_services "ms-account" "ms-person"
        ;;
    "help"|*)
        echo "Watch Services Script"
        echo ""
        echo "Usage: ./watch-services.sh [SERVICE_NAME|PRESET]"
        echo ""
        echo "Individual Services:"
        echo "  ms-account     Start ms-account in watch mode"
        echo "  ms-person      Start ms-person in watch mode"
        echo "  ms-wallet      Start ms-wallet in watch mode"
        echo "  ms-evaluation  Start ms-evaluation in watch mode"
        echo "  ms-stock       Start ms-stock in watch mode"
        echo "  ms-purchase    Start ms-purchase in watch mode"
        echo "  mf-home        Start mf-home in watch mode"
        echo ""
        echo "Presets:"
        echo "  essential      Start essential services (account, person)"
        echo "  all-services   Start all microservices"
        echo ""
        echo "Note: Service repositories must be cloned in the parent directory"
        echo "Example structure:"
        echo "  parent-dir/"
        echo "    ├── thrift/"
        echo "    ├── ms-account/"
        echo "    ├── ms-person/"
        echo "    └── ..."
        ;;
esac
