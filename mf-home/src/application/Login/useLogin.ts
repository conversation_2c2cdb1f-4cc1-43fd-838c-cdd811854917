import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  type LoginSchemaType,
  useLoginSchema,
} from '@app/application/Login/Login.schemas'
import { useLoginMutation } from '@app/domain/Login'
import { useToast } from '@thrift/design-system/packages/molecules/Toast'
import { useLanguage } from '@app/application/Language'
import { useAuthStore } from '@app/domain/stores/useAuth.store'
import { useNavigate } from 'react-router-dom'

export const useLogin = () => {
  const { showToast } = useToast()
  const { translate } = useLanguage()
  const LoginSchema = useLoginSchema()
  const navigate = useNavigate()

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginSchemaType>({
    resolver: zodResolver(LoginSchema),
  })

  const { postLoginAsync, loading } = useLoginMutation()

  const onSubmit = async (params: LoginSchemaType) => {
    try {
      const data = await postLoginAsync(params)

      useAuthStore.getState().setUser(data)
      showToast({
        title: translate('login:loginSuccessTitle'),
        message: translate('login:loginSuccessMessage', {
          email: data.email,
        }),
        type: 'success',
      })

      navigate('/backoffice')
    } catch {
      showToast({
        title: translate('login:loginFailedTitle'),
        message: translate('login:loginFailedMessage'),
        type: 'error',
      })
    }
  }

  return {
    handleSubmit: handleSubmit(onSubmit),
    register,
    errors,
    loading,
  }
}
