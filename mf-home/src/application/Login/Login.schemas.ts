import { z } from 'zod'
import { InputPasswordSchema } from '@thrift/design-system/packages/molecules/InputPassword'
import { InputEmailSchema } from '@thrift/design-system/packages/molecules/InputEmail'
import { useLanguage } from '@app/application/Language'

export const LoginSchemaBase = z.object({
  email: z.string(),
  password: z.string(),
})

export type LoginSchemaType = z.infer<typeof LoginSchemaBase>

export const useLoginSchema = () => {
  const { translate } = useLanguage()

  return z.object({
    email: InputEmailSchema({
      max: { value: 50, message: translate('login:fieldMax', { max: 50 }) },
      min: { value: 5, message: translate('login:fieldMin', { min: 5 }) },
      required: { value: true, message: translate('login:fieldRequired') },
      emailMessage: translate('login:emailInvalid'),
    }),
    password: InputPasswordSchema({
      max: { value: 20, message: translate('login:fieldMax', { max: 20 }) },
      min: { value: 8, message: translate('login:fieldMin', { min: 8 }) },
      required: { value: true, message: translate('login:fieldRequired') },
    }),
  })
}
