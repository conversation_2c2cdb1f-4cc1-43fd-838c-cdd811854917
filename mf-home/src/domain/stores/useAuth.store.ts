import { create } from 'zustand'
import {
  createJSONStorage,
  persist,
  type StateStorage,
} from 'zustand/middleware'
import type { IAuthState } from '@app/domain/stores/types/auth'
import type { LoginResponse } from '@app/domain/Login/Login'

const AuthStorage: StateStorage = {
  setItem: (name, value) => {
    return localStorage.set(name, value)
  },
  getItem: (name) => {
    const value = localStorage.getString(name)

    return value ?? null
  },
  removeItem: (name) => {
    return localStorage.removeItem(name)
  },
}

const initialState = {
  user: {} as LoginResponse,
  token: '',
}

export const useAuthStore = create<IAuthState>()(
  persist(
    (set) => ({
      ...initialState,
      setToken: (token) => set({ token }),
      setUser: (user) => set({ user }),
      reset: () => set(initialState),
    }),
    {
      name: 'auth-storage',
      storage: createJSONStorage(() => AuthStorage),
    },
  ),
)
