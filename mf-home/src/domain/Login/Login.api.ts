import type { LoginParams, LoginResponse } from '@app/domain/Login/Login'
import api from '@app/domain/services/api'
import { useAuthStore } from '@app/domain/stores/useAuth.store'

export const postLogin = async (
  params: LoginParams,
): Promise<LoginResponse> => {
  const response = await api.post<LoginResponse>(
    '/account/authenticate',
    params,
  )

  const headers = response.headers

  const authToken = headers['authorization'] || headers['Authorization']

  useAuthStore.getState().setToken(authToken)

  return response.data
}
