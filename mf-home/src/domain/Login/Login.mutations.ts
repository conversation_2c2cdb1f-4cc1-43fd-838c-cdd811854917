import { useCallback } from 'react'
import { useMutation } from '@tanstack/react-query'
import type { LoginParams, LoginResponse } from '@app/domain/Login/Login'
import { postLogin } from '@app/domain/Login/Login.api'

export const useLoginMutation = () => {
  const { mutateAsync, isPending } = useMutation<
    LoginResponse,
    Error,
    LoginParams
  >({
    mutationFn: postLogin,
  })

  const postLoginAsync = useCallback(
    async (props: LoginParams): Promise<LoginResponse> => {
      const response = await mutateAsync(props)

      return response
    },
    [mutateAsync],
  )

  return {
    postLoginAsync,
    loading: isPending,
  }
}
