import { LoginWrap } from '@thrift/design-system/packages/templates/Login'
import { SignIn } from '@thrift/design-system/packages/organisms/SignIn'
import { SignInBrandInfo } from '@thrift/design-system/packages/organisms/SignInBrandInfo'
import { But<PERSON> } from '@thrift/design-system/packages/molecules/Button'
import { InputPassword } from '@thrift/design-system/packages/molecules/InputPassword'
import { InputEmail } from '@thrift/design-system/packages/molecules/InputEmail'
import { useLogin } from '@app/application/Login'
import { useLanguage } from '@app/application/Language'
import { Typography } from '@thrift/design-system/packages/molecules/Typography'

export const Login = () => {
  const { handleSubmit, register, errors, loading } = useLogin()
  const { translate } = useLanguage()

  return (
    <LoginWrap>
      <SignInBrandInfo />
      <SignIn>
        <Typography variant="h4" weight="bold">
          {translate('login:welcome')}
        </Typography>
        <Typography>{translate('login:description')}</Typography>
        <InputEmail
          {...register('email')}
          label={translate('login:email')}
          errorMessage={errors.email?.message}
        />
        <InputPassword
          {...register('password')}
          label={translate('login:password')}
          errorMessage={errors.password?.message}
        />
        <Button type="button" onClick={handleSubmit} loading={loading}>
          {translate('login:button')}
        </Button>
      </SignIn>
    </LoginWrap>
  )
}
