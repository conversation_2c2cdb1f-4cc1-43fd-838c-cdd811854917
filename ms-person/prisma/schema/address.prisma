model Address {
  id         String   @id @default(uuid())
  person     Person   @relation(fields: [personId], references: [id])
  personId   String   @unique
  line1      String
  number     String
  line2      String
  city       String
  region     String
  country    String
  postalCode String
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  deleted    Boolean  @default(false)

  @@map("addresses")
}
