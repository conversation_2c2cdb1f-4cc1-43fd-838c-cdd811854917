import type { FilterStoreCreditDto } from '@app/application/StoreCredit'
import { FilterStoreCreditSchema } from '@app/application/StoreCredit/StoreCredit.schema'

import { StoreCredit } from '@app/domain/database/StoreCredit'

export class GetStoreCreditBalance {
  public async getBalance(personId: string, input?: unknown) {
    if (!personId) {
      throw new ReferenceError('Person ID is required')
    }

    let filters: FilterStoreCreditDto = { page: undefined, perPage: undefined }

    if (input) {
      filters = FilterStoreCreditSchema.parse(input)
    }

    const model = new StoreCredit()

    const [storeCredits, totalBalance] = await Promise.all([
      model.findByPersonId(personId, {
        issuingStoreLegalPersonId: filters.issuingStoreLegalPersonId,
      }),
      model.getTotalBalance(personId, filters.issuingStoreLegalPersonId),
    ])

    return {
      storeCredits: storeCredits.map((credit) => ({
        ...credit,
        balance: Number(credit.balance),
      })),
      totalBalance: Number(totalBalance),
    }
  }

  public async find(input: unknown) {
    const filter: FilterStoreCreditDto = FilterStoreCreditSchema.parse(input)

    const model = new StoreCredit()

    const result = await model.find(filter)

    return {
      ...result,
      data: result.data.map((credit) => ({
        ...credit,
        balance: Number(credit.balance),
      })),
    }
  }
}
