import { z } from 'zod'

import { createFilterSchema } from '@thrift/common/engines/Pagination'

export const PersonNaturalBaseSchema = z.object({
  name: z.string().min(1),
  gender: z.string().min(1),
  birthDate: z.coerce.date(),
})

export const CreatePersonNaturalSchema = PersonNaturalBaseSchema

export const UpdatePersonNaturalSchema = PersonNaturalBaseSchema.extend({
  id: z.string().uuid(),
  personId: z.string().uuid(),
}).partial({
  name: true,
  gender: true,
  birthDate: true,
})

export const FilterPersonNaturalSchema = createFilterSchema({
  name: z.string().optional(),
  gender: z.string().optional(),
})
