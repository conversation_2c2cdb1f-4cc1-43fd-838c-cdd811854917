# How Thrift Works with Macro-Services
Thrift macro-services set of standards, documentation about backend services

## You'll need to read
- [Github Personal Access Token](../external/github.md#github-personal-access-token)
- [Install Docker](../external/docker.md#install)
- [Up Dabase Service](https://github.com/thrift-technology/simulator/blob/main/README.md#database)

## About the Source (`src`) Package

The approach here (_Application Architecture_) was using the _[Clean Architecture](https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html)_ as the base of the application, where:

- _**domain**_ package represents the **Enterprise Business Rules**;
- _**application**_ package represents the **Application Business Rules**;
- _**resources**_ package represents the **Application Business Rules**;
- _**app.config.json** (copy/paste **app.config.sample.json** file)_ has the basic parameters to run the application. Just see the file; no doubt you will understand.

### About the Other Packages

- _**eslint.config.js**_ configuration to _[eslint](https://eslint.org)_;
- _**.vscode**_ workspace configuration to [vscode](https://code.visualstudio.com/docs/editor/workspaces#_singlefolder-workspace-settings).

## How to use this Application

### Database
Read how to up the database service [here](https://github.com/thrift-technology/simulator/blob/main/README.md#database)

### provisioning

_[Provisioning](https://en.wikipedia.org/wiki/Provisioning_(technology))_ is the process which involves the automated setup of infrastructure, including servers, networks, and storage, alongside the deployment and configuration of the macro/micro-services themselves, ensuring a scalable and secure environment.

```bash
$ docker-compose run --rm provisioning
```

After the items below, The `./package.json` file has the `scripts` property, there can see all app commands.
