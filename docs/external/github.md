# How <PERSON><PERSON><PERSON> uses Github
Thrift's github set of standards, documentation about version control

## Github Personal Access Token
[Creating a personal access token (classic)](https://docs.github.com/en/authentication/keeping-your-account-and-data-secure/managing-your-personal-access-tokens#creating-a-personal-access-token-classic) in the [github](https://github.com/).
- **Note:** `personal-token-github`
- **Expiration:** `No expiration`
- **Select scopes:** `notifications`, `read:packages`, `repo`, and `workflow`

After that, inside in the directory repository, execute the command below:
```bash
$ export PERSONAL_TOKEN_GITHUB=<<personal-token-github>>
$ git config --global url."https://$<EMAIL>/thrift-technology/".insteadOf "https://github.com/thrift-technology/"
```
*Replace the `<<personal-token-github>>` to your Github Personal Access Token*
